// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function () {
  // Initialize all functionality
  initMobileMenu();
  initSmoothScrolling();
  initFormValidation();
  initScrollEffects();
  initAnimations();
  initConversionTracking();
});

// Mobile Menu Functionality
function initMobileMenu() {
  const hamburger = document.getElementById('hamburger');
  const navMenu = document.getElementById('nav-menu');
  const navLinks = document.querySelectorAll('.nav-link');

  if (hamburger && navMenu) {
    hamburger.addEventListener('click', function () {
      hamburger.classList.toggle('active');
      navMenu.classList.toggle('active');
    });

    // Close menu when clicking on nav links
    navLinks.forEach((link) => {
      link.addEventListener('click', function () {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function (e) {
      if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
      }
    });
  }
}

// Smooth Scrolling for Navigation Links
function initSmoothScrolling() {
  const navLinks = document.querySelectorAll('a[href^="#"]');

  navLinks.forEach((link) => {
    link.addEventListener('click', function (e) {
      e.preventDefault();

      const targetId = this.getAttribute('href');
      const targetSection = document.querySelector(targetId);

      if (targetSection) {
        const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar

        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth',
        });
      }
    });
  });
}

// Form Validation and Submission
function initFormValidation() {
  const contactForm = document.getElementById('contact-form');

  if (contactForm) {
    contactForm.addEventListener('submit', function (e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(contactForm);
      const data = Object.fromEntries(formData);

      // Validate required fields
      const requiredFields = ['name', 'email', 'phone', 'location'];
      let isValid = true;
      let firstErrorField = null;

      requiredFields.forEach((field) => {
        const input = document.getElementById(field);
        const value = data[field]?.trim();

        if (!value) {
          showFieldError(input, 'This field is required');
          isValid = false;
          if (!firstErrorField) firstErrorField = input;
        } else {
          clearFieldError(input);
        }
      });

      // Validate email format
      const emailInput = document.getElementById('email');
      const emailValue = data.email?.trim();
      if (emailValue && !isValidEmail(emailValue)) {
        showFieldError(emailInput, 'Please enter a valid email address');
        isValid = false;
        if (!firstErrorField) firstErrorField = emailInput;
      }

      // Validate phone format
      const phoneInput = document.getElementById('phone');
      const phoneValue = data.phone?.trim();
      if (phoneValue && !isValidPhone(phoneValue)) {
        showFieldError(phoneInput, 'Please enter a valid phone number');
        isValid = false;
        if (!firstErrorField) firstErrorField = phoneInput;
      }

      if (isValid) {
        submitForm(data);
      } else if (firstErrorField) {
        firstErrorField.focus();
      }
    });

    // Real-time validation
    const inputs = contactForm.querySelectorAll('input[required]');
    inputs.forEach((input) => {
      input.addEventListener('blur', function () {
        validateField(this);
      });

      input.addEventListener('input', function () {
        clearFieldError(this);
      });
    });
  }
}

// Field Validation Functions
function validateField(field) {
  const value = field.value.trim();
  const fieldName = field.name;

  if (!value) {
    showFieldError(field, 'This field is required');
    return false;
  }

  if (fieldName === 'email' && !isValidEmail(value)) {
    showFieldError(field, 'Please enter a valid email address');
    return false;
  }

  if (fieldName === 'phone' && !isValidPhone(value)) {
    showFieldError(field, 'Please enter a valid phone number');
    return false;
  }

  clearFieldError(field);
  return true;
}

function showFieldError(field, message) {
  clearFieldError(field);

  field.style.borderColor = '#dc2626';

  const errorDiv = document.createElement('div');
  errorDiv.className = 'field-error';
  errorDiv.style.color = '#dc2626';
  errorDiv.style.fontSize = '0.8rem';
  errorDiv.style.marginTop = '0.25rem';
  errorDiv.textContent = message;

  field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
  field.style.borderColor = '#e5e7eb';

  const existingError = field.parentNode.querySelector('.field-error');
  if (existingError) {
    existingError.remove();
  }
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidPhone(phone) {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
  return cleanPhone.length >= 10 && phoneRegex.test(cleanPhone);
}

// Form Submission
function submitForm(data) {
  const submitButton = document.querySelector(
    '#contact-form button[type="submit"]'
  );
  const originalText = submitButton.textContent;

  // Show loading state
  submitButton.textContent = 'Sending...';
  submitButton.disabled = true;

  // Track conversion
  trackConversion('form_submission', data);

  // Simulate form submission (replace with actual endpoint)
  setTimeout(() => {
    showSuccessMessage();
    document.getElementById('contact-form').reset();

    // Reset button
    submitButton.textContent = originalText;
    submitButton.disabled = false;
  }, 2000);
}

function showSuccessMessage() {
  const formContainer = document.querySelector('.contact-form-container');

  const successDiv = document.createElement('div');
  successDiv.className = 'success-message';
  successDiv.style.cssText = `
        background: #dcfce7;
        border: 2px solid #22c55e;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    `;

  successDiv.innerHTML = `
        <h3 style="color: #16a34a; margin-bottom: 0.5rem;">Thank You!</h3>
        <p style="color: #15803d; margin: 0;">We've received your information and will contact you within 24 hours to schedule your no-obligation call.</p>
    `;

  formContainer.insertBefore(successDiv, formContainer.firstChild);

  // Remove success message after 10 seconds
  setTimeout(() => {
    successDiv.remove();
  }, 10000);
}

// Scroll Effects
function initScrollEffects() {
  const navbar = document.getElementById('navbar');

  window.addEventListener('scroll', function () {
    if (window.scrollY > 100) {
      navbar.style.background = 'rgba(255, 255, 255, 0.98)';
      navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
      navbar.style.background = 'rgba(255, 255, 255, 0.95)';
      navbar.style.boxShadow = 'none';
    }
  });
}

// Animations
function initAnimations() {
  // Animate elements when they come into view
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';

        // Animate bars in charts
        if (entry.target.classList.contains('growth-chart')) {
          animateGrowthChart();
        }
        if (entry.target.classList.contains('calls-chart')) {
          animateCallsChart();
        }
      }
    });
  }, observerOptions);

  // Observe elements for animation
  const animatedElements = document.querySelectorAll(
    '.service-card, .benefit-item, .growth-chart, .calls-chart'
  );
  animatedElements.forEach((el) => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
  });
}

function animateGrowthChart() {
  const bars = document.querySelectorAll('.growth-chart .bar');
  bars.forEach((bar, index) => {
    setTimeout(() => {
      bar.style.transition = 'height 0.8s ease';
      const height = bar.getAttribute('data-height');
      bar.style.height = height;
    }, index * 200);
  });
}

function animateCallsChart() {
  const bars = document.querySelectorAll('.calls-chart .bar');
  bars.forEach((bar, index) => {
    setTimeout(() => {
      bar.style.transition = 'height 0.6s ease';
      const height = bar.getAttribute('data-height');
      bar.style.height = height;
    }, index * 150);
  });
}

// Conversion Tracking
function initConversionTracking() {
  // Track page view
  trackConversion('page_view');

  // Track CTA clicks
  const ctaButtons = document.querySelectorAll('.btn-primary');
  ctaButtons.forEach((button) => {
    button.addEventListener('click', function () {
      const buttonText = this.textContent.trim();
      trackConversion('cta_click', { button_text: buttonText });
    });
  });

  // Track scroll depth
  let maxScrollDepth = 0;
  const scrollMilestones = [25, 50, 75, 90];

  window.addEventListener('scroll', function () {
    const scrollPercent = Math.round(
      (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
    );

    if (scrollPercent > maxScrollDepth) {
      maxScrollDepth = scrollPercent;

      scrollMilestones.forEach((milestone) => {
        if (scrollPercent >= milestone && !window[`tracked_${milestone}`]) {
          trackConversion('scroll_depth', { depth: milestone });
          window[`tracked_${milestone}`] = true;
        }
      });
    }
  });
}

function trackConversion(event, data = {}) {
  // Console log for development (replace with actual analytics)
  console.log('Conversion Event:', event, data);

  // Example: Google Analytics 4
  if (typeof gtag !== 'undefined') {
    gtag('event', event, {
      event_category: 'landscaping_seo',
      event_label: data.button_text || data.depth || 'general',
      value: 1,
    });
  }

  // Example: Facebook Pixel
  if (typeof fbq !== 'undefined') {
    fbq('track', 'Lead', data);
  }
}

// Utility Functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
