/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  line-height: 1.7;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

.btn-secondary {
  background: transparent;
  color: #22c55e;
  border: 2px solid #22c55e;
}

.btn-secondary:hover {
  background: #22c55e;
  color: white;
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.1rem;
}

.btn-full {
  width: 100%;
  padding: 16px;
  font-size: 1.1rem;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 1.5rem;
  color: #22c55e;
}

.logo-icon {
  font-size: 2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #22c55e;
}

.cta-nav {
  background: #22c55e;
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cta-nav:hover {
  background: #16a34a;
  transform: translateY(-1px);
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  transition: 0.3s;
  border-radius: 2px;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-title {
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.highlight {
  color: #22c55e;
  position: relative;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat {
  text-align: center;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #22c55e;
}

.stat-text {
  font-size: 0.9rem;
  color: #6b7280;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.hero-trust {
  font-size: 0.9rem;
  color: #6b7280;
}

.trust-text {
  margin: 0;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-result-mockup {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.search-bar {
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #6b7280;
}

.search-results {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.result {
  padding: 12px;
  border-radius: 6px;
  background: #f9fafb;
  color: #6b7280;
}

.result.featured {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  font-weight: 600;
}

.result-title {
  display: block;
  font-weight: 600;
}

.result-rating {
  display: block;
  font-size: 0.9rem;
  margin-top: 4px;
}

/* Problem Section */
.problem {
  padding: 80px 0;
  background: #f9fafb;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
  color: #1f2937;
}

.problem-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.problem-item {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.problem-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.problem-item h3 {
  color: #dc2626;
  margin-bottom: 1rem;
}

.problem-item p {
  color: #6b7280;
  margin: 0;
}

/* Services Section */
.services {
  padding: 80px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  margin: 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.service-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #22c55e;
}

.service-card.featured {
  border-color: #22c55e;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
}

.service-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #22c55e;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.service-card h3 {
  color: #1f2937;
  margin-bottom: 1rem;
}

.service-card p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.service-features {
  list-style: none;
  text-align: left;
}

.service-features li {
  padding: 0.5rem 0;
  color: #374151;
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

/* Benefits Section */
.benefits {
  padding: 80px 0;
  background: #f9fafb;
}

.benefits-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.benefit-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.benefit-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.benefit-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.benefit-content h3 {
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.benefit-content p {
  color: #6b7280;
  margin: 0;
}

.benefits-visual {
  display: flex;
  justify-content: center;
}

.growth-chart {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.growth-chart h4 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1f2937;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 200px;
  gap: 1rem;
}

.bar {
  background: linear-gradient(to top, #22c55e, #16a34a);
  border-radius: 4px 4px 0 0;
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: end;
  min-height: 20%;
  transition: all 0.3s ease;
}

.bar:hover {
  transform: scale(1.05);
}

.bar-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.8rem;
  color: #6b7280;
  white-space: nowrap;
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.9rem;
  font-weight: 600;
  color: #22c55e;
  white-space: nowrap;
}

/* Testimonials Section */
.testimonials {
  padding: 80px 0;
  background: white;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: #f9fafb;
  padding: 2rem;
  border-radius: 16px;
  border-left: 4px solid #22c55e;
}

.testimonial-rating {
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.testimonial-text {
  font-style: italic;
  color: #374151;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-info h4 {
  color: #1f2937;
  margin: 0;
  font-size: 1rem;
}

.author-info span {
  color: #6b7280;
  font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  text-align: center;
}

.cta-content h2 {
  color: white;
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-content .btn-primary {
  background: white;
  color: #22c55e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-content .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.cta-guarantee {
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background: #f9fafb;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info h2 {
  color: #1f2937;
  margin-bottom: 1rem;
}

.contact-info p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.contact-features {
  margin-bottom: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.feature-icon {
  color: #22c55e;
  font-weight: bold;
}

.contact-urgency {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 2rem;
}

.contact-urgency p {
  margin: 0;
  color: #92400e;
}

.contact-form-container {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-disclaimer {
  font-size: 0.8rem;
  color: #6b7280;
  text-align: center;
  margin: 0;
}

/* Footer */
.footer {
  background: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-brand p {
  color: #9ca3af;
  margin-bottom: 0;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 1.5rem;
  color: #22c55e;
  margin-bottom: 1rem;
}

.footer-links h4,
.footer-contact h4 {
  color: white;
  margin-bottom: 1rem;
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #22c55e;
}

.footer-contact p {
  color: #9ca3af;
  margin-bottom: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: #9ca3af;
  margin: 0;
}

.footer-bottom a {
  color: #22c55e;
  text-decoration: none;
}

.footer-bottom a:hover {
  text-decoration: underline;
}

/* Responsive Design */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }

  .hero-container {
    max-width: 1400px;
  }

  .hero-title {
    font-size: 4.5rem;
  }

  .section-title {
    font-size: 3.5rem;
  }
}

/* Desktop (1024px - 1399px) */
@media (max-width: 1399px) {
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .hero-cta {
    justify-content: center;
  }

  .benefits-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
    gap: 1rem;
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(7px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-7px) rotate(-45deg);
  }
}

/* Mobile (480px - 767px) */
@media (max-width: 767px) {
  .hero {
    padding: 100px 0 60px;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
  }

  .problem-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    height: 150px;
  }

  .bar-label {
    font-size: 0.7rem;
  }

  .bar-value {
    font-size: 0.8rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .search-result-mockup {
    max-width: 300px;
  }
}

/* Small Mobile (320px - 479px) */
@media (max-width: 479px) {
  .container {
    padding: 0 15px;
  }

  .nav-container {
    padding: 1rem 15px;
  }

  .hero {
    padding: 90px 0 50px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .service-card,
  .testimonial-card,
  .contact-form-container {
    padding: 1.5rem;
  }

  .growth-chart {
    padding: 1.5rem;
  }

  .chart-bars {
    height: 120px;
    gap: 0.5rem;
  }

  .hero-cta .btn {
    font-size: 1rem;
    padding: 14px 24px;
  }
}

/* Animation for bars */
@keyframes growBar {
  from {
    height: 0;
  }
  to {
    height: var(--bar-height);
  }
}

.bar[data-height='20%'] {
  height: 20%;
}

.bar[data-height='45%'] {
  height: 45%;
}

.bar[data-height='80%'] {
  height: 80%;
}

.bar[data-height='100%'] {
  height: 100%;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-0 {
  margin-bottom: 0;
}

.mt-2 {
  margin-top: 2rem;
}

/* Focus states for accessibility */
.btn:focus,
input:focus,
select:focus {
  outline: 2px solid #22c55e;
  outline-offset: 2px;
}

/* Smooth scrolling for anchor links */
html {
  scroll-padding-top: 80px;
}
